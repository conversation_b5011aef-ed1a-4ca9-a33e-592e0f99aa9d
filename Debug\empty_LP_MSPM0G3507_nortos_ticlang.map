******************************************************************************
            TI ARM Clang Linker PC v4.0.3                      
******************************************************************************
>> Linked Tue Jul 22 14:38:51 2025

OUTPUT FILE NAME:   <empty_LP_MSPM0G3507_nortos_ticlang.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 00000229


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  00000320  0001fce0  R  X
  SRAM                  20200000   00008000  00000201  00007dff  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00000320   00000320    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00000238   00000238    r-x .text
  000002f8    000002f8    00000010   00000010    r-- .rodata
  00000308    00000308    00000018   00000018    r-- .cinit
20200000    20200000    00000001   00000000    rw-
  20200000    20200000    00000001   00000000    rw- .bss
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00000238     
                  000000c0    00000070     ti_msp_dl_config.o (.text.SYSCFG_DL_UART_0_init)
                  00000130    00000048     driverlib.a : dl_uart.o (.text.DL_UART_init)
                  00000178    00000040     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  000001b8    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  000001f4    00000034     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  00000228    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  00000250    0000001c     empty.o (.text.main)
                  0000026c    00000018     empty.o (.text.UART0_IRQHandler)
                  00000284    00000016     libc.a : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init_nomemset)
                  0000029a    00000002     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  0000029c    00000014     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  000002b0    00000014     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  000002c4    00000012     driverlib.a : dl_uart.o (.text.DL_UART_setClockConfig)
                  000002d6    0000000a                 : dl_common.o (.text.DL_Common_delayCycles)
                  000002e0    00000006     libc.a : exit.c.obj (.text:abort)
                  000002e6    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  000002ea    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  000002ee    00000004     libc.a : pre_init.c.obj (.text._system_pre_init)
                  000002f2    00000006     --HOLE-- [fill = 0]

.cinit     0    00000308    00000018     
                  00000308    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  00000310    00000004     (__TI_handler_table)
                  00000314    00000008     (__TI_cinit_table)
                  0000031c    00000004     --HOLE-- [fill = 0]

.rodata    0    000002f8    00000010     
                  000002f8    0000000a     ti_msp_dl_config.o (.rodata.gUART_0Config)
                  00000302    00000002     ti_msp_dl_config.o (.rodata.gUART_0ClockConfig)
                  00000304    00000004     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    00000001     UNINITIALIZED
                  20200000    00000001     (.common:gEchoData)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code   ro data   rw data
       ------                         ----   -------   -------
    .\
       ti_msp_dl_config.o             268    12        0      
       startup_mspm0g350x_ticlang.o   6      192       0      
       empty.o                        52     0         1      
    +--+------------------------------+------+---------+---------+
       Total:                         326    204       1      
                                                              
    F:/TIM/mspm0_sdk_2_05_01_00/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_uart.o                      90     0         0      
       dl_common.o                    10     0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         100    0         0      
                                                              
    F:\CCS_20.2.0.00012_win\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       autoinit.c.obj                 60     0         0      
       boot_cortex_m.c.obj            40     0         0      
       copy_zero_init.c.obj           22     0         0      
       exit.c.obj                     6      0         0      
       pre_init.c.obj                 4      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         132    0         0      
                                                              
    F:\CCS_20.2.0.00012_win\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                 4      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         4      0         0      
                                                              
       Stack:                         0      0         512    
       Linker Generated:              0      20        0      
    +--+------------------------------+------+---------+---------+
       Grand Total:                   562    224       513    


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 00000314 records: 1, size/record: 8, table size: 8
	.bss: load addr=00000308, load size=00000008 bytes, run addr=20200000, run size=00000001 bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 00000310 records: 1, size/record: 4, table size: 4
	index: 0, handler: __TI_zero_init


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                          
-------   ----                          
0000029b  ADC0_IRQHandler               
0000029b  ADC1_IRQHandler               
0000029b  AES_IRQHandler                
000002e6  C$$EXIT                       
0000029b  CANFD0_IRQHandler             
0000029b  DAC0_IRQHandler               
000002d7  DL_Common_delayCycles         
00000131  DL_UART_init                  
000002c5  DL_UART_setClockConfig        
0000029b  DMA_IRQHandler                
0000029b  Default_Handler               
0000029b  GROUP0_IRQHandler             
0000029b  GROUP1_IRQHandler             
000002e7  HOSTexit                      
0000029b  HardFault_Handler             
0000029b  I2C0_IRQHandler               
0000029b  I2C1_IRQHandler               
0000029b  NMI_Handler                   
0000029b  PendSV_Handler                
0000029b  RTC_IRQHandler                
000002eb  Reset_Handler                 
0000029b  SPI0_IRQHandler               
0000029b  SPI1_IRQHandler               
0000029b  SVC_Handler                   
0000029d  SYSCFG_DL_GPIO_init           
00000179  SYSCFG_DL_SYSCTL_init         
000000c1  SYSCFG_DL_UART_0_init         
000002b1  SYSCFG_DL_init                
000001f5  SYSCFG_DL_initPower           
0000029b  SysTick_Handler               
0000029b  TIMA0_IRQHandler              
0000029b  TIMA1_IRQHandler              
0000029b  TIMG0_IRQHandler              
0000029b  TIMG12_IRQHandler             
0000029b  TIMG6_IRQHandler              
0000029b  TIMG7_IRQHandler              
0000029b  TIMG8_IRQHandler              
0000026d  UART0_IRQHandler              
0000029b  UART1_IRQHandler              
0000029b  UART2_IRQHandler              
0000029b  UART3_IRQHandler              
20208000  __STACK_END                   
00000200  __STACK_SIZE                  
00000000  __TI_ATRegion0_region_sz      
00000000  __TI_ATRegion0_src_addr       
00000000  __TI_ATRegion0_trg_addr       
00000000  __TI_ATRegion1_region_sz      
00000000  __TI_ATRegion1_src_addr       
00000000  __TI_ATRegion1_trg_addr       
00000000  __TI_ATRegion2_region_sz      
00000000  __TI_ATRegion2_src_addr       
00000000  __TI_ATRegion2_trg_addr       
00000314  __TI_CINIT_Base               
0000031c  __TI_CINIT_Limit              
0000031c  __TI_CINIT_Warm               
00000310  __TI_Handler_Table_Base       
00000314  __TI_Handler_Table_Limit      
000001b9  __TI_auto_init_nobinit_nopinit
ffffffff  __TI_pprof_out_hndl           
ffffffff  __TI_prof_data_size           
ffffffff  __TI_prof_data_start          
00000000  __TI_static_base__            
00000285  __TI_zero_init_nomemset       
ffffffff  __binit__                     
UNDEFED   __mpu_init                    
20207e00  __stack                       
20200000  __start___llvm_prf_bits       
20200000  __start___llvm_prf_cnts       
20200000  __stop___llvm_prf_bits        
20200000  __stop___llvm_prf_cnts        
00000229  _c_int00_noargs               
UNDEFED   _system_post_cinit            
000002ef  _system_pre_init              
000002e1  abort                         
ffffffff  binit                         
20200000  gEchoData                     
00000000  interruptVectors              
00000251  main                          


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                          
-------   ----                          
00000000  __TI_ATRegion0_region_sz      
00000000  __TI_ATRegion0_src_addr       
00000000  __TI_ATRegion0_trg_addr       
00000000  __TI_ATRegion1_region_sz      
00000000  __TI_ATRegion1_src_addr       
00000000  __TI_ATRegion1_trg_addr       
00000000  __TI_ATRegion2_region_sz      
00000000  __TI_ATRegion2_src_addr       
00000000  __TI_ATRegion2_trg_addr       
00000000  __TI_static_base__            
00000000  interruptVectors              
000000c1  SYSCFG_DL_UART_0_init         
00000131  DL_UART_init                  
00000179  SYSCFG_DL_SYSCTL_init         
000001b9  __TI_auto_init_nobinit_nopinit
000001f5  SYSCFG_DL_initPower           
00000200  __STACK_SIZE                  
00000229  _c_int00_noargs               
00000251  main                          
0000026d  UART0_IRQHandler              
00000285  __TI_zero_init_nomemset       
0000029b  ADC0_IRQHandler               
0000029b  ADC1_IRQHandler               
0000029b  AES_IRQHandler                
0000029b  CANFD0_IRQHandler             
0000029b  DAC0_IRQHandler               
0000029b  DMA_IRQHandler                
0000029b  Default_Handler               
0000029b  GROUP0_IRQHandler             
0000029b  GROUP1_IRQHandler             
0000029b  HardFault_Handler             
0000029b  I2C0_IRQHandler               
0000029b  I2C1_IRQHandler               
0000029b  NMI_Handler                   
0000029b  PendSV_Handler                
0000029b  RTC_IRQHandler                
0000029b  SPI0_IRQHandler               
0000029b  SPI1_IRQHandler               
0000029b  SVC_Handler                   
0000029b  SysTick_Handler               
0000029b  TIMA0_IRQHandler              
0000029b  TIMA1_IRQHandler              
0000029b  TIMG0_IRQHandler              
0000029b  TIMG12_IRQHandler             
0000029b  TIMG6_IRQHandler              
0000029b  TIMG7_IRQHandler              
0000029b  TIMG8_IRQHandler              
0000029b  UART1_IRQHandler              
0000029b  UART2_IRQHandler              
0000029b  UART3_IRQHandler              
0000029d  SYSCFG_DL_GPIO_init           
000002b1  SYSCFG_DL_init                
000002c5  DL_UART_setClockConfig        
000002d7  DL_Common_delayCycles         
000002e1  abort                         
000002e6  C$$EXIT                       
000002e7  HOSTexit                      
000002eb  Reset_Handler                 
000002ef  _system_pre_init              
00000310  __TI_Handler_Table_Base       
00000314  __TI_CINIT_Base               
00000314  __TI_Handler_Table_Limit      
0000031c  __TI_CINIT_Limit              
0000031c  __TI_CINIT_Warm               
20200000  __start___llvm_prf_bits       
20200000  __start___llvm_prf_cnts       
20200000  __stop___llvm_prf_bits        
20200000  __stop___llvm_prf_cnts        
20200000  gEchoData                     
20207e00  __stack                       
20208000  __STACK_END                   
ffffffff  __TI_pprof_out_hndl           
ffffffff  __TI_prof_data_size           
ffffffff  __TI_prof_data_start          
ffffffff  __binit__                     
ffffffff  binit                         
UNDEFED   __mpu_init                    
UNDEFED   _system_post_cinit            

[78 symbols]
