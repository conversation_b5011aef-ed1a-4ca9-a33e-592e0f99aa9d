<?xml version="1.0" encoding="ISO-8859-1" ?>
<link_info>
   <banner>TI ARM Clang Linker PC v4.0.3.LTS</banner>
   <copyright>Copyright (c) 1996-2018 Texas Instruments Incorporated</copyright>
   <command_line>F:\CCS_20.2.0.00012_win\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\bin\tiarmlnk -IF:\CCS_20.2.0.00012_win\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib -o empty_LP_MSPM0G3507_nortos_ticlang.out -mempty_LP_MSPM0G3507_nortos_ticlang.map -iF:/TIM/mspm0_sdk_2_05_01_00/source -iC:/Users/<USER>/workspace_ccstheia/empty_LP_MSPM0G3507_nortos_ticlang -iC:/Users/<USER>/workspace_ccstheia/empty_LP_MSPM0G3507_nortos_ticlang/Debug/syscfg -iF:/CCS_20.2.0.00012_win/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib --diag_wrap=off --display_error_number --warn_sections --xml_link_info=empty_LP_MSPM0G3507_nortos_ticlang_linkInfo.xml --rom_model ./empty.o ./ti_msp_dl_config.o ./startup_mspm0g350x_ticlang.o -l./device_linker.cmd -ldevice.cmd.genlibs -llibc.a --start-group -llibc++.a -llibc++abi.a -llibc.a -llibsys.a -llibsysbm.a -llibclang_rt.builtins.a -llibclang_rt.profile.a --end-group --cg_opt_level=2</command_line>
   <link_time>0x687f31fb</link_time>
   <link_errors>0x0</link_errors>
   <output_file>C:\Users\<USER>\workspace_ccstheia\empty_LP_MSPM0G3507_nortos_ticlang\Debug\empty_LP_MSPM0G3507_nortos_ticlang.out</output_file>
   <entry_point>
      <name>_c_int00_noargs</name>
      <address>0x229</address>
   </entry_point>
   <input_file_list>
      <input_file id="fl-1">
         <path>C:\Users\<USER>\workspace_ccstheia\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\</path>
         <kind>object</kind>
         <file>empty.o</file>
         <name>empty.o</name>
      </input_file>
      <input_file id="fl-2">
         <path>C:\Users\<USER>\workspace_ccstheia\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\</path>
         <kind>object</kind>
         <file>ti_msp_dl_config.o</file>
         <name>ti_msp_dl_config.o</name>
      </input_file>
      <input_file id="fl-3">
         <path>C:\Users\<USER>\workspace_ccstheia\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\</path>
         <kind>object</kind>
         <file>startup_mspm0g350x_ticlang.o</file>
         <name>startup_mspm0g350x_ticlang.o</name>
      </input_file>
      <input_file id="fl-10">
         <path>C:\Users\<USER>\workspace_ccstheia\empty_LP_MSPM0G3507_nortos_ticlang\Debug\</path>
         <kind>object</kind>
         <file>&lt;internal&gt;</file>
         <name>&lt;internal&gt;</name>
      </input_file>
      <input_file id="fl-11">
         <path>F:\TIM\mspm0_sdk_2_05_01_00\source\ti\driverlib\lib\ticlang\m0p\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_common.o</name>
      </input_file>
      <input_file id="fl-12">
         <path>F:\TIM\mspm0_sdk_2_05_01_00\source\ti\driverlib\lib\ticlang\m0p\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_uart.o</name>
      </input_file>
      <input_file id="fl-29">
         <path>F:\CCS_20.2.0.00012_win\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>boot_cortex_m.c.obj</name>
      </input_file>
      <input_file id="fl-2a">
         <path>F:\CCS_20.2.0.00012_win\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>mathacl_init.c.obj</name>
      </input_file>
      <input_file id="fl-2b">
         <path>F:\CCS_20.2.0.00012_win\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>autoinit.c.obj</name>
      </input_file>
      <input_file id="fl-2c">
         <path>F:\CCS_20.2.0.00012_win\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>pre_init.c.obj</name>
      </input_file>
      <input_file id="fl-2d">
         <path>F:\CCS_20.2.0.00012_win\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>cpy_tbl.c.obj</name>
      </input_file>
      <input_file id="fl-2e">
         <path>F:\CCS_20.2.0.00012_win\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_zero_init.c.obj</name>
      </input_file>
      <input_file id="fl-2f">
         <path>F:\CCS_20.2.0.00012_win\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_decompress_none.c.obj</name>
      </input_file>
      <input_file id="fl-30">
         <path>F:\CCS_20.2.0.00012_win\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_decompress_lzss.c.obj</name>
      </input_file>
      <input_file id="fl-31">
         <path>F:\CCS_20.2.0.00012_win\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>exit.c.obj</name>
      </input_file>
      <input_file id="fl-32">
         <path>F:\CCS_20.2.0.00012_win\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>_lock.c.obj</name>
      </input_file>
      <input_file id="fl-33">
         <path>F:\CCS_20.2.0.00012_win\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>args_main.c.obj</name>
      </input_file>
      <input_file id="fl-d3">
         <path>F:\CCS_20.2.0.00012_win\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>hostexit.c.obj</name>
      </input_file>
      <input_file id="fl-d4">
         <path>F:\CCS_20.2.0.00012_win\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_memcpy.S.obj</name>
      </input_file>
      <input_file id="fl-d5">
         <path>F:\CCS_20.2.0.00012_win\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_memset.S.obj</name>
      </input_file>
      <input_file id="fl-d6">
         <path>F:\CCS_20.2.0.00012_win\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_uidivmod.S.obj</name>
      </input_file>
      <input_file id="fl-d7">
         <path>F:\CCS_20.2.0.00012_win\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_div0.c.obj</name>
      </input_file>
      <input_file id="fl-d8">
         <path>F:\CCS_20.2.0.00012_win\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>memcpy16.S.obj</name>
      </input_file>
      <input_file id="fl-d9">
         <path>F:\CCS_20.2.0.00012_win\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>memset16.S.obj</name>
      </input_file>
   </input_file_list>
   <object_component_list>
      <object_component id="oc-18">
         <name>.intvecs</name>
         <load_address>0x0</load_address>
         <readonly>true</readonly>
         <run_address>0x0</run_address>
         <size>0xc0</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-a9">
         <name>.text.SYSCFG_DL_UART_0_init</name>
         <load_address>0xc0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xc0</run_address>
         <size>0x70</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-c7">
         <name>.text.DL_UART_init</name>
         <load_address>0x130</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x130</run_address>
         <size>0x48</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-a7">
         <name>.text.SYSCFG_DL_SYSCTL_init</name>
         <load_address>0x178</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x178</run_address>
         <size>0x40</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-8c">
         <name>.text:__TI_auto_init_nobinit_nopinit</name>
         <load_address>0x1b8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1b8</run_address>
         <size>0x3c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2b"/>
      </object_component>
      <object_component id="oc-a5">
         <name>.text.SYSCFG_DL_initPower</name>
         <load_address>0x1f4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1f4</run_address>
         <size>0x34</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-54">
         <name>.text:_c_int00_noargs</name>
         <load_address>0x228</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x228</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-29"/>
      </object_component>
      <object_component id="oc-6b">
         <name>.text.main</name>
         <load_address>0x250</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x250</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-34">
         <name>.text.UART0_IRQHandler</name>
         <load_address>0x26c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x26c</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-4b">
         <name>.text:decompress:ZI:__TI_zero_init_nomemset</name>
         <load_address>0x284</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x284</run_address>
         <size>0x16</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-33">
         <name>.text.Default_Handler</name>
         <load_address>0x29a</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x29a</run_address>
         <size>0x2</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-a6">
         <name>.text.SYSCFG_DL_GPIO_init</name>
         <load_address>0x29c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x29c</run_address>
         <size>0x14</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-80">
         <name>.text.SYSCFG_DL_init</name>
         <load_address>0x2b0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2b0</run_address>
         <size>0x14</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-c1">
         <name>.text.DL_UART_setClockConfig</name>
         <load_address>0x2c4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2c4</run_address>
         <size>0x12</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-bd">
         <name>.text.DL_Common_delayCycles</name>
         <load_address>0x2d6</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2d6</run_address>
         <size>0xa</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-11"/>
      </object_component>
      <object_component id="oc-85">
         <name>.text:abort</name>
         <load_address>0x2e0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2e0</run_address>
         <size>0x6</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-ac">
         <name>.text.HOSTexit</name>
         <load_address>0x2e6</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2e6</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-d3"/>
      </object_component>
      <object_component id="oc-2f">
         <name>.text.Reset_Handler</name>
         <load_address>0x2ea</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2ea</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-67">
         <name>.text._system_pre_init</name>
         <load_address>0x2ee</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2ee</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2c"/>
      </object_component>
      <object_component id="oc-11b">
         <name>.cinit..bss.load</name>
         <load_address>0x308</load_address>
         <readonly>true</readonly>
         <run_address>0x308</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-119">
         <name>__TI_handler_table</name>
         <load_address>0x310</load_address>
         <readonly>true</readonly>
         <run_address>0x310</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-11a">
         <name>__TI_cinit_table</name>
         <load_address>0x314</load_address>
         <readonly>true</readonly>
         <run_address>0x314</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-c9">
         <name>.rodata.gUART_0Config</name>
         <load_address>0x2f8</load_address>
         <readonly>true</readonly>
         <run_address>0x2f8</run_address>
         <size>0xa</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-c8">
         <name>.rodata.gUART_0ClockConfig</name>
         <load_address>0x302</load_address>
         <readonly>true</readonly>
         <run_address>0x302</run_address>
         <size>0x2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-e3">
         <name>.binit</name>
         <load_address>0x0</load_address>
         <readonly>true</readonly>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-5b">
         <name>.common:gEchoData</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20200000</run_address>
         <size>0x1</size>
         <alignment>0x1</alignment>
      </object_component>
      <object_component id="oc-1a">
         <name>.stack</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20207e00</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-29"/>
      </object_component>
      <object_component id="oc-11d">
         <name>.stack</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20207e00</run_address>
         <size>0x0</size>
         <alignment>0x8</alignment>
      </object_component>
      <object_component id="oc-5c">
         <name>.debug_abbrev</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x175</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-aa">
         <name>.debug_abbrev</name>
         <load_address>0x175</load_address>
         <run_address>0x175</run_address>
         <size>0x1ba</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-39">
         <name>.debug_abbrev</name>
         <load_address>0x32f</load_address>
         <run_address>0x32f</run_address>
         <size>0x6d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-d5">
         <name>.debug_abbrev</name>
         <load_address>0x39c</load_address>
         <run_address>0x39c</run_address>
         <size>0x62</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11"/>
      </object_component>
      <object_component id="oc-d8">
         <name>.debug_abbrev</name>
         <load_address>0x3fe</load_address>
         <run_address>0x3fe</run_address>
         <size>0x29b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-3b">
         <name>.debug_abbrev</name>
         <load_address>0x699</load_address>
         <run_address>0x699</run_address>
         <size>0xaf</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-29"/>
      </object_component>
      <object_component id="oc-b6">
         <name>.debug_abbrev</name>
         <load_address>0x748</load_address>
         <run_address>0x748</run_address>
         <size>0x170</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2b"/>
      </object_component>
      <object_component id="oc-7e">
         <name>.debug_abbrev</name>
         <load_address>0x8b8</load_address>
         <run_address>0x8b8</run_address>
         <size>0x39</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2c"/>
      </object_component>
      <object_component id="oc-60">
         <name>.debug_abbrev</name>
         <load_address>0x8f1</load_address>
         <run_address>0x8f1</run_address>
         <size>0xc2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-b0">
         <name>.debug_abbrev</name>
         <load_address>0x9b3</load_address>
         <run_address>0x9b3</run_address>
         <size>0x98</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-ca">
         <name>.debug_abbrev</name>
         <load_address>0xa4b</load_address>
         <run_address>0xa4b</run_address>
         <size>0x2c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d3"/>
      </object_component>
      <object_component id="oc-11f">
         <name>.debug_abbrev</name>
         <load_address>0xa77</load_address>
         <run_address>0xa77</run_address>
         <size>0xf</size>
         <alignment>0x0</alignment>
      </object_component>
      <object_component id="oc-35">
         <name>.debug_info</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x80b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-82">
         <name>.debug_info</name>
         <load_address>0x80b</load_address>
         <run_address>0x80b</run_address>
         <size>0x1a64</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-19">
         <name>.debug_info</name>
         <load_address>0x226f</load_address>
         <run_address>0x226f</run_address>
         <size>0x80</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-be">
         <name>.debug_info</name>
         <load_address>0x22ef</load_address>
         <run_address>0x22ef</run_address>
         <size>0x75</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11"/>
      </object_component>
      <object_component id="oc-c6">
         <name>.debug_info</name>
         <load_address>0x2364</load_address>
         <run_address>0x2364</run_address>
         <size>0x12a6</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-1b">
         <name>.debug_info</name>
         <load_address>0x360a</load_address>
         <run_address>0x360a</run_address>
         <size>0x423</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-29"/>
      </object_component>
      <object_component id="oc-90">
         <name>.debug_info</name>
         <load_address>0x3a2d</load_address>
         <run_address>0x3a2d</run_address>
         <size>0x744</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2b"/>
      </object_component>
      <object_component id="oc-69">
         <name>.debug_info</name>
         <load_address>0x4171</load_address>
         <run_address>0x4171</run_address>
         <size>0x46</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2c"/>
      </object_component>
      <object_component id="oc-4d">
         <name>.debug_info</name>
         <load_address>0x41b7</load_address>
         <run_address>0x41b7</run_address>
         <size>0x192</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-86">
         <name>.debug_info</name>
         <load_address>0x4349</load_address>
         <run_address>0x4349</run_address>
         <size>0xf8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-ae">
         <name>.debug_info</name>
         <load_address>0x4441</load_address>
         <run_address>0x4441</run_address>
         <size>0x3b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d3"/>
      </object_component>
      <object_component id="oc-11e">
         <name>.debug_info</name>
         <load_address>0x447c</load_address>
         <run_address>0x447c</run_address>
         <size>0xbe</size>
         <alignment>0x0</alignment>
      </object_component>
      <object_component id="oc-36">
         <name>.debug_ranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-81">
         <name>.debug_ranges</name>
         <load_address>0x18</load_address>
         <run_address>0x18</run_address>
         <size>0x30</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-30">
         <name>.debug_ranges</name>
         <load_address>0x48</load_address>
         <run_address>0x48</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-c2">
         <name>.debug_ranges</name>
         <load_address>0x60</load_address>
         <run_address>0x60</run_address>
         <size>0x1a8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-3e">
         <name>.debug_ranges</name>
         <load_address>0x208</load_address>
         <run_address>0x208</run_address>
         <size>0x48</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-29"/>
      </object_component>
      <object_component id="oc-8f">
         <name>.debug_ranges</name>
         <load_address>0x250</load_address>
         <run_address>0x250</run_address>
         <size>0x48</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2b"/>
      </object_component>
      <object_component id="oc-50">
         <name>.debug_ranges</name>
         <load_address>0x298</load_address>
         <run_address>0x298</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-89">
         <name>.debug_ranges</name>
         <load_address>0x2b0</load_address>
         <run_address>0x2b0</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-5d">
         <name>.debug_str</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x5f5</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-ab">
         <name>.debug_str</name>
         <load_address>0x5f5</load_address>
         <run_address>0x5f5</run_address>
         <size>0x15a1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-3a">
         <name>.debug_str</name>
         <load_address>0x1b96</load_address>
         <run_address>0x1b96</run_address>
         <size>0x176</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-d6">
         <name>.debug_str</name>
         <load_address>0x1d0c</load_address>
         <run_address>0x1d0c</run_address>
         <size>0x178</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11"/>
      </object_component>
      <object_component id="oc-d9">
         <name>.debug_str</name>
         <load_address>0x1e84</load_address>
         <run_address>0x1e84</run_address>
         <size>0xcee</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-3c">
         <name>.debug_str</name>
         <load_address>0x2b72</load_address>
         <run_address>0x2b72</run_address>
         <size>0x225</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-29"/>
      </object_component>
      <object_component id="oc-b7">
         <name>.debug_str</name>
         <load_address>0x2d97</load_address>
         <run_address>0x2d97</run_address>
         <size>0x32f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2b"/>
      </object_component>
      <object_component id="oc-7f">
         <name>.debug_str</name>
         <load_address>0x30c6</load_address>
         <run_address>0x30c6</run_address>
         <size>0xf5</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2c"/>
      </object_component>
      <object_component id="oc-61">
         <name>.debug_str</name>
         <load_address>0x31bb</load_address>
         <run_address>0x31bb</run_address>
         <size>0x19b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-b1">
         <name>.debug_str</name>
         <load_address>0x3356</load_address>
         <run_address>0x3356</run_address>
         <size>0x148</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-cb">
         <name>.debug_str</name>
         <load_address>0x349e</load_address>
         <run_address>0x349e</run_address>
         <size>0xe9</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d3"/>
      </object_component>
      <object_component id="oc-37">
         <name>.debug_frame</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-84">
         <name>.debug_frame</name>
         <load_address>0x30</load_address>
         <run_address>0x30</run_address>
         <size>0x78</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-31">
         <name>.debug_frame</name>
         <load_address>0xa8</load_address>
         <run_address>0xa8</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-bf">
         <name>.debug_frame</name>
         <load_address>0xd8</load_address>
         <run_address>0xd8</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-11"/>
      </object_component>
      <object_component id="oc-c5">
         <name>.debug_frame</name>
         <load_address>0xf8</load_address>
         <run_address>0xf8</run_address>
         <size>0x1b8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-53">
         <name>.debug_frame</name>
         <load_address>0x2b0</load_address>
         <run_address>0x2b0</run_address>
         <size>0x90</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-29"/>
      </object_component>
      <object_component id="oc-91">
         <name>.debug_frame</name>
         <load_address>0x340</load_address>
         <run_address>0x340</run_address>
         <size>0x100</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2b"/>
      </object_component>
      <object_component id="oc-6a">
         <name>.debug_frame</name>
         <load_address>0x440</load_address>
         <run_address>0x440</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2c"/>
      </object_component>
      <object_component id="oc-4e">
         <name>.debug_frame</name>
         <load_address>0x460</load_address>
         <run_address>0x460</run_address>
         <size>0x38</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-88">
         <name>.debug_frame</name>
         <load_address>0x498</load_address>
         <run_address>0x498</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-af">
         <name>.debug_frame</name>
         <load_address>0x4c8</load_address>
         <run_address>0x4c8</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-d3"/>
      </object_component>
      <object_component id="oc-38">
         <name>.debug_line</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x27e</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-83">
         <name>.debug_line</name>
         <load_address>0x27e</load_address>
         <run_address>0x27e</run_address>
         <size>0x46f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-32">
         <name>.debug_line</name>
         <load_address>0x6ed</load_address>
         <run_address>0x6ed</run_address>
         <size>0xb9</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-c0">
         <name>.debug_line</name>
         <load_address>0x7a6</load_address>
         <run_address>0x7a6</run_address>
         <size>0x179</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11"/>
      </object_component>
      <object_component id="oc-c3">
         <name>.debug_line</name>
         <load_address>0x91f</load_address>
         <run_address>0x91f</run_address>
         <size>0xa18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-3d">
         <name>.debug_line</name>
         <load_address>0x1337</load_address>
         <run_address>0x1337</run_address>
         <size>0x1dc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-29"/>
      </object_component>
      <object_component id="oc-8e">
         <name>.debug_line</name>
         <load_address>0x1513</load_address>
         <run_address>0x1513</run_address>
         <size>0x51a</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2b"/>
      </object_component>
      <object_component id="oc-68">
         <name>.debug_line</name>
         <load_address>0x1a2d</load_address>
         <run_address>0x1a2d</run_address>
         <size>0x3e</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2c"/>
      </object_component>
      <object_component id="oc-4f">
         <name>.debug_line</name>
         <load_address>0x1a6b</load_address>
         <run_address>0x1a6b</run_address>
         <size>0xfe</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-87">
         <name>.debug_line</name>
         <load_address>0x1b69</load_address>
         <run_address>0x1b69</run_address>
         <size>0x67</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-ad">
         <name>.debug_line</name>
         <load_address>0x1bd0</load_address>
         <run_address>0x1bd0</run_address>
         <size>0x41</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d3"/>
      </object_component>
      <object_component id="oc-a8">
         <name>.debug_loc</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xee</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-d7">
         <name>.debug_loc</name>
         <load_address>0xee</load_address>
         <run_address>0xee</run_address>
         <size>0x13</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11"/>
      </object_component>
      <object_component id="oc-c4">
         <name>.debug_loc</name>
         <load_address>0x101</load_address>
         <run_address>0x101</run_address>
         <size>0x7bc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-3f">
         <name>.debug_loc</name>
         <load_address>0x8bd</load_address>
         <run_address>0x8bd</run_address>
         <size>0xd8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-29"/>
      </object_component>
      <object_component id="oc-8d">
         <name>.debug_loc</name>
         <load_address>0x995</load_address>
         <run_address>0x995</run_address>
         <size>0x424</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2b"/>
      </object_component>
      <object_component id="oc-4c">
         <name>.debug_loc</name>
         <load_address>0xdb9</load_address>
         <run_address>0xdb9</run_address>
         <size>0x16c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-8b">
         <name>.debug_loc</name>
         <load_address>0xf25</load_address>
         <run_address>0xf25</run_address>
         <size>0x26</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
   </object_component_list>
   <logical_group_list>
      <logical_group id="lg-2" display="no" color="cyan">
         <name>.intvecs</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xc0</size>
         <contents>
            <object_component_ref idref="oc-18"/>
         </contents>
      </logical_group>
      <logical_group id="lg-3" display="no" color="cyan">
         <name>.text</name>
         <load_address>0xc0</load_address>
         <run_address>0xc0</run_address>
         <size>0x238</size>
         <contents>
            <object_component_ref idref="oc-a9"/>
            <object_component_ref idref="oc-c7"/>
            <object_component_ref idref="oc-a7"/>
            <object_component_ref idref="oc-8c"/>
            <object_component_ref idref="oc-a5"/>
            <object_component_ref idref="oc-54"/>
            <object_component_ref idref="oc-6b"/>
            <object_component_ref idref="oc-34"/>
            <object_component_ref idref="oc-4b"/>
            <object_component_ref idref="oc-33"/>
            <object_component_ref idref="oc-a6"/>
            <object_component_ref idref="oc-80"/>
            <object_component_ref idref="oc-c1"/>
            <object_component_ref idref="oc-bd"/>
            <object_component_ref idref="oc-85"/>
            <object_component_ref idref="oc-ac"/>
            <object_component_ref idref="oc-2f"/>
            <object_component_ref idref="oc-67"/>
         </contents>
      </logical_group>
      <logical_group id="lg-4" display="no" color="cyan">
         <name>.const</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-5" display="no" color="cyan">
         <name>.cinit</name>
         <load_address>0x308</load_address>
         <run_address>0x308</run_address>
         <size>0x18</size>
         <contents>
            <object_component_ref idref="oc-11b"/>
            <object_component_ref idref="oc-119"/>
            <object_component_ref idref="oc-11a"/>
         </contents>
      </logical_group>
      <logical_group id="lg-6" display="no" color="cyan">
         <name>.pinit</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-7" display="no" color="cyan">
         <name>.rodata</name>
         <load_address>0x2f8</load_address>
         <run_address>0x2f8</run_address>
         <size>0x10</size>
         <contents>
            <object_component_ref idref="oc-c9"/>
            <object_component_ref idref="oc-c8"/>
         </contents>
      </logical_group>
      <logical_group id="lg-8" display="no" color="cyan">
         <name>.ARM.exidx</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-9" display="no" color="cyan">
         <name>.init_array</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-a" display="no" color="cyan">
         <name>.binit</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
            <object_component_ref idref="oc-e3"/>
         </contents>
      </logical_group>
      <logical_group id="lg-b" display="no" color="cyan">
         <name>.TI.ramfunc</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-c" display="no" color="cyan">
         <name>.vtable</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-d" display="no" color="cyan">
         <name>.args</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-e" display="no" color="cyan">
         <name>.data</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-f" display="no" color="cyan">
         <name>.bss</name>
         <run_address>0x20200000</run_address>
         <size>0x1</size>
         <contents>
            <object_component_ref idref="oc-5b"/>
         </contents>
      </logical_group>
      <logical_group id="lg-10" display="no" color="cyan">
         <name>.sysmem</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-11" display="no" color="cyan">
         <name>.stack</name>
         <run_address>0x20207e00</run_address>
         <size>0x200</size>
         <contents>
            <object_component_ref idref="oc-1a"/>
            <object_component_ref idref="oc-11d"/>
         </contents>
      </logical_group>
      <logical_group id="lg-12" display="no" color="cyan">
         <name>.BCRConfig</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-13" display="no" color="cyan">
         <name>.BSLConfig</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-da" display="no" color="cyan">
         <name>.TI.noinit</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-db" display="no" color="cyan">
         <name>.TI.persistent</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-dc" display="no" color="cyan">
         <name>.TI.local</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-dd" display="no" color="cyan">
         <name>.TI.onchip</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-de" display="no" color="cyan">
         <name>.TI.offchip</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-df" display="no" color="cyan">
         <name>__llvm_prf_cnts</name>
         <run_address>0x20200000</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-e1" display="no" color="cyan">
         <name>__llvm_prf_bits</name>
         <run_address>0x20200000</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-fd" display="never" color="cyan">
         <name>.debug_abbrev</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xa86</size>
         <contents>
            <object_component_ref idref="oc-5c"/>
            <object_component_ref idref="oc-aa"/>
            <object_component_ref idref="oc-39"/>
            <object_component_ref idref="oc-d5"/>
            <object_component_ref idref="oc-d8"/>
            <object_component_ref idref="oc-3b"/>
            <object_component_ref idref="oc-b6"/>
            <object_component_ref idref="oc-7e"/>
            <object_component_ref idref="oc-60"/>
            <object_component_ref idref="oc-b0"/>
            <object_component_ref idref="oc-ca"/>
            <object_component_ref idref="oc-11f"/>
         </contents>
      </logical_group>
      <logical_group id="lg-ff" display="never" color="cyan">
         <name>.debug_info</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x453a</size>
         <contents>
            <object_component_ref idref="oc-35"/>
            <object_component_ref idref="oc-82"/>
            <object_component_ref idref="oc-19"/>
            <object_component_ref idref="oc-be"/>
            <object_component_ref idref="oc-c6"/>
            <object_component_ref idref="oc-1b"/>
            <object_component_ref idref="oc-90"/>
            <object_component_ref idref="oc-69"/>
            <object_component_ref idref="oc-4d"/>
            <object_component_ref idref="oc-86"/>
            <object_component_ref idref="oc-ae"/>
            <object_component_ref idref="oc-11e"/>
         </contents>
      </logical_group>
      <logical_group id="lg-101" display="never" color="cyan">
         <name>.debug_ranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x2c8</size>
         <contents>
            <object_component_ref idref="oc-36"/>
            <object_component_ref idref="oc-81"/>
            <object_component_ref idref="oc-30"/>
            <object_component_ref idref="oc-c2"/>
            <object_component_ref idref="oc-3e"/>
            <object_component_ref idref="oc-8f"/>
            <object_component_ref idref="oc-50"/>
            <object_component_ref idref="oc-89"/>
         </contents>
      </logical_group>
      <logical_group id="lg-103" display="never" color="cyan">
         <name>.debug_str</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x3587</size>
         <contents>
            <object_component_ref idref="oc-5d"/>
            <object_component_ref idref="oc-ab"/>
            <object_component_ref idref="oc-3a"/>
            <object_component_ref idref="oc-d6"/>
            <object_component_ref idref="oc-d9"/>
            <object_component_ref idref="oc-3c"/>
            <object_component_ref idref="oc-b7"/>
            <object_component_ref idref="oc-7f"/>
            <object_component_ref idref="oc-61"/>
            <object_component_ref idref="oc-b1"/>
            <object_component_ref idref="oc-cb"/>
         </contents>
      </logical_group>
      <logical_group id="lg-105" display="never" color="cyan">
         <name>.debug_frame</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x4e8</size>
         <contents>
            <object_component_ref idref="oc-37"/>
            <object_component_ref idref="oc-84"/>
            <object_component_ref idref="oc-31"/>
            <object_component_ref idref="oc-bf"/>
            <object_component_ref idref="oc-c5"/>
            <object_component_ref idref="oc-53"/>
            <object_component_ref idref="oc-91"/>
            <object_component_ref idref="oc-6a"/>
            <object_component_ref idref="oc-4e"/>
            <object_component_ref idref="oc-88"/>
            <object_component_ref idref="oc-af"/>
         </contents>
      </logical_group>
      <logical_group id="lg-107" display="never" color="cyan">
         <name>.debug_line</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x1c11</size>
         <contents>
            <object_component_ref idref="oc-38"/>
            <object_component_ref idref="oc-83"/>
            <object_component_ref idref="oc-32"/>
            <object_component_ref idref="oc-c0"/>
            <object_component_ref idref="oc-c3"/>
            <object_component_ref idref="oc-3d"/>
            <object_component_ref idref="oc-8e"/>
            <object_component_ref idref="oc-68"/>
            <object_component_ref idref="oc-4f"/>
            <object_component_ref idref="oc-87"/>
            <object_component_ref idref="oc-ad"/>
         </contents>
      </logical_group>
      <logical_group id="lg-109" display="never" color="cyan">
         <name>.debug_loc</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xf4b</size>
         <contents>
            <object_component_ref idref="oc-a8"/>
            <object_component_ref idref="oc-d7"/>
            <object_component_ref idref="oc-c4"/>
            <object_component_ref idref="oc-3f"/>
            <object_component_ref idref="oc-8d"/>
            <object_component_ref idref="oc-4c"/>
            <object_component_ref idref="oc-8b"/>
         </contents>
      </logical_group>
      <logical_group id="lg-11c" display="no" color="cyan">
         <name>Veneer$$CMSE</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <load_segment id="lg-123" display="no" color="cyan">
         <name>SEGMENT_0</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x320</size>
         <flags>0x5</flags>
         <contents>
            <logical_group_ref idref="lg-2"/>
            <logical_group_ref idref="lg-3"/>
            <logical_group_ref idref="lg-7"/>
            <logical_group_ref idref="lg-5"/>
         </contents>
      </load_segment>
      <load_segment id="lg-124" display="no" color="cyan">
         <name>SEGMENT_1</name>
         <run_address>0x20200000</run_address>
         <size>0x1</size>
         <flags>0x6</flags>
         <contents>
            <logical_group_ref idref="lg-f"/>
         </contents>
      </load_segment>
      <load_segment id="lg-125" display="no" color="cyan">
         <name>SEGMENT_2</name>
         <run_address>0x20207e00</run_address>
         <size>0x200</size>
         <flags>0x6</flags>
         <contents>
            <logical_group_ref idref="lg-11"/>
         </contents>
      </load_segment>
   </logical_group_list>
   <placement_map>
      <memory_area display="yes" color="green">
         <name>FLASH</name>
         <page_id>0x0</page_id>
         <origin>0x0</origin>
         <length>0x20000</length>
         <used_space>0x320</used_space>
         <unused_space>0x1fce0</unused_space>
         <attributes>RX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x0</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-a"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x0</start_address>
               <size>0xc0</size>
               <logical_group_ref idref="lg-2"/>
            </allocated_space>
            <allocated_space>
               <start_address>0xc0</start_address>
               <size>0x238</size>
               <logical_group_ref idref="lg-3"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x2f8</start_address>
               <size>0x10</size>
               <logical_group_ref idref="lg-7"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x308</start_address>
               <size>0x18</size>
               <logical_group_ref idref="lg-5"/>
            </allocated_space>
            <available_space>
               <start_address>0x320</start_address>
               <size>0x1fce0</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>SRAM</name>
         <page_id>0x0</page_id>
         <origin>0x20200000</origin>
         <length>0x8000</length>
         <used_space>0x201</used_space>
         <unused_space>0x7dff</unused_space>
         <attributes>RWX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x20200000</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-df"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x20200000</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-e1"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x20200000</start_address>
               <size>0x1</size>
               <logical_group_ref idref="lg-f"/>
            </allocated_space>
            <available_space>
               <start_address>0x20200001</start_address>
               <size>0x7dff</size>
            </available_space>
            <allocated_space>
               <start_address>0x20207e00</start_address>
               <size>0x200</size>
               <logical_group_ref idref="lg-11"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>BCR_CONFIG</name>
         <page_id>0x0</page_id>
         <origin>0x41c00000</origin>
         <length>0xff</length>
         <used_space>0x0</used_space>
         <unused_space>0xff</unused_space>
         <attributes>R</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>BSL_CONFIG</name>
         <page_id>0x0</page_id>
         <origin>0x41c00100</origin>
         <length>0x80</length>
         <used_space>0x0</used_space>
         <unused_space>0x80</unused_space>
         <attributes>R</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
   </placement_map>
   <cptbl_list>
      <cptbl>
         <name>__TI_cinit_table</name>
         <cprec>
            <name>.bss</name>
            <load_address>0x308</load_address>
            <load_size>0x8</load_size>
            <run_address>0x20200000</run_address>
            <run_size>0x1</run_size>
            <compression>zero_init</compression>
         </cprec>
      </cptbl>
   </cptbl_list>
   <handler_table>
      <handler_table_name>__TI_handler_table</handler_table_name>
      <handler>
         <index>0x0</index>
         <name>__TI_zero_init</name>
      </handler>
   </handler_table>
   <symbol_table>
      <symbol id="sm-1">
         <name>__start___llvm_prf_cnts</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-2">
         <name>__stop___llvm_prf_cnts</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-3">
         <name>__start___llvm_prf_bits</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-4">
         <name>__stop___llvm_prf_bits</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-5">
         <name>__TI_CINIT_Base</name>
         <value>0x314</value>
      </symbol>
      <symbol id="sm-6">
         <name>__TI_CINIT_Limit</name>
         <value>0x31c</value>
      </symbol>
      <symbol id="sm-7">
         <name>__TI_CINIT_Warm</name>
         <value>0x31c</value>
      </symbol>
      <symbol id="sm-8">
         <name>__TI_Handler_Table_Base</name>
         <value>0x310</value>
      </symbol>
      <symbol id="sm-9">
         <name>__TI_Handler_Table_Limit</name>
         <value>0x314</value>
      </symbol>
      <symbol id="sm-a">
         <name>binit</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-b">
         <name>__binit__</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-c">
         <name>__STACK_SIZE</name>
         <value>0x200</value>
      </symbol>
      <symbol id="sm-d">
         <name>__STACK_END</name>
         <value>0x20208000</value>
      </symbol>
      <symbol id="sm-e">
         <name>__TI_pprof_out_hndl</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-f">
         <name>__TI_prof_data_start</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-10">
         <name>__TI_prof_data_size</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-3e">
         <name>main</name>
         <value>0x251</value>
         <object_component_ref idref="oc-6b"/>
      </symbol>
      <symbol id="sm-3f">
         <name>UART0_IRQHandler</name>
         <value>0x26d</value>
         <object_component_ref idref="oc-34"/>
      </symbol>
      <symbol id="sm-40">
         <name>gEchoData</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-5a">
         <name>SYSCFG_DL_init</name>
         <value>0x2b1</value>
         <object_component_ref idref="oc-80"/>
      </symbol>
      <symbol id="sm-5b">
         <name>SYSCFG_DL_initPower</name>
         <value>0x1f5</value>
         <object_component_ref idref="oc-a5"/>
      </symbol>
      <symbol id="sm-5c">
         <name>SYSCFG_DL_GPIO_init</name>
         <value>0x29d</value>
         <object_component_ref idref="oc-a6"/>
      </symbol>
      <symbol id="sm-5d">
         <name>SYSCFG_DL_SYSCTL_init</name>
         <value>0x179</value>
         <object_component_ref idref="oc-a7"/>
      </symbol>
      <symbol id="sm-5e">
         <name>SYSCFG_DL_UART_0_init</name>
         <value>0xc1</value>
         <object_component_ref idref="oc-a9"/>
      </symbol>
      <symbol id="sm-69">
         <name>Default_Handler</name>
         <value>0x29b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-6a">
         <name>Reset_Handler</name>
         <value>0x2eb</value>
         <object_component_ref idref="oc-2f"/>
      </symbol>
      <symbol id="sm-6b">
         <name>interruptVectors</name>
         <value>0x0</value>
         <object_component_ref idref="oc-18"/>
      </symbol>
      <symbol id="sm-6c">
         <name>NMI_Handler</name>
         <value>0x29b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-6d">
         <name>HardFault_Handler</name>
         <value>0x29b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-6e">
         <name>SVC_Handler</name>
         <value>0x29b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-6f">
         <name>PendSV_Handler</name>
         <value>0x29b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-70">
         <name>SysTick_Handler</name>
         <value>0x29b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-71">
         <name>GROUP0_IRQHandler</name>
         <value>0x29b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-72">
         <name>GROUP1_IRQHandler</name>
         <value>0x29b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-73">
         <name>TIMG8_IRQHandler</name>
         <value>0x29b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-74">
         <name>UART3_IRQHandler</name>
         <value>0x29b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-75">
         <name>ADC0_IRQHandler</name>
         <value>0x29b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-76">
         <name>ADC1_IRQHandler</name>
         <value>0x29b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-77">
         <name>CANFD0_IRQHandler</name>
         <value>0x29b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-78">
         <name>DAC0_IRQHandler</name>
         <value>0x29b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-79">
         <name>SPI0_IRQHandler</name>
         <value>0x29b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-7a">
         <name>SPI1_IRQHandler</name>
         <value>0x29b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-7b">
         <name>UART1_IRQHandler</name>
         <value>0x29b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-7c">
         <name>UART2_IRQHandler</name>
         <value>0x29b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-7d">
         <name>TIMG0_IRQHandler</name>
         <value>0x29b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-7e">
         <name>TIMG6_IRQHandler</name>
         <value>0x29b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-7f">
         <name>TIMA0_IRQHandler</name>
         <value>0x29b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-80">
         <name>TIMA1_IRQHandler</name>
         <value>0x29b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-81">
         <name>TIMG7_IRQHandler</name>
         <value>0x29b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-82">
         <name>TIMG12_IRQHandler</name>
         <value>0x29b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-83">
         <name>I2C0_IRQHandler</name>
         <value>0x29b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-84">
         <name>I2C1_IRQHandler</name>
         <value>0x29b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-85">
         <name>AES_IRQHandler</name>
         <value>0x29b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-86">
         <name>RTC_IRQHandler</name>
         <value>0x29b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-87">
         <name>DMA_IRQHandler</name>
         <value>0x29b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-88">
         <name>__TI_ATRegion0_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-89">
         <name>__TI_ATRegion0_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-8a">
         <name>__TI_ATRegion0_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-8b">
         <name>__TI_ATRegion1_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-8c">
         <name>__TI_ATRegion1_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-8d">
         <name>__TI_ATRegion1_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-8e">
         <name>__TI_ATRegion2_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-8f">
         <name>__TI_ATRegion2_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-90">
         <name>__TI_ATRegion2_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-99">
         <name>DL_Common_delayCycles</name>
         <value>0x2d7</value>
         <object_component_ref idref="oc-bd"/>
      </symbol>
      <symbol id="sm-a6">
         <name>DL_UART_init</name>
         <value>0x131</value>
         <object_component_ref idref="oc-c7"/>
      </symbol>
      <symbol id="sm-a7">
         <name>DL_UART_setClockConfig</name>
         <value>0x2c5</value>
         <object_component_ref idref="oc-c1"/>
      </symbol>
      <symbol id="sm-b2">
         <name>_c_int00_noargs</name>
         <value>0x229</value>
         <object_component_ref idref="oc-54"/>
      </symbol>
      <symbol id="sm-b3">
         <name>__stack</name>
         <value>0x20207e00</value>
         <object_component_ref idref="oc-1a"/>
      </symbol>
      <symbol id="sm-bf">
         <name>__TI_auto_init_nobinit_nopinit</name>
         <value>0x1b9</value>
         <object_component_ref idref="oc-8c"/>
      </symbol>
      <symbol id="sm-c7">
         <name>_system_pre_init</name>
         <value>0x2ef</value>
         <object_component_ref idref="oc-67"/>
      </symbol>
      <symbol id="sm-d2">
         <name>__TI_zero_init_nomemset</name>
         <value>0x285</value>
         <object_component_ref idref="oc-4b"/>
      </symbol>
      <symbol id="sm-de">
         <name>abort</name>
         <value>0x2e1</value>
         <object_component_ref idref="oc-85"/>
      </symbol>
      <symbol id="sm-e8">
         <name>HOSTexit</name>
         <value>0x2e7</value>
         <object_component_ref idref="oc-ac"/>
      </symbol>
      <symbol id="sm-e9">
         <name>C$$EXIT</name>
         <value>0x2e6</value>
         <object_component_ref idref="oc-ac"/>
      </symbol>
      <symbol id="sm-eb">
         <name>__TI_static_base__</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-ed">
         <name>__mpu_init</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-ee">
         <name>_system_post_cinit</name>
         <value>0x0</value>
      </symbol>
   </symbol_table>
   <title>Link successful</title>
</link_info>
